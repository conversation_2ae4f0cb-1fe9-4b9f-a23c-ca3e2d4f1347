import * as k8s from '@kubernetes/client-node';
import { PgResource } from './resource-type.js';

const GROUP = 'db.seabury.app';
const VERSION = 'v1';
const PLURAL = 'postgresresources';

const getApis = async () => {
  const kc = new k8s.KubeConfig();
  const k8sApi = kc.makeApiClient(k8s.CoreV1Api);
  const customObjectsApi = kc.makeApiClient(k8s.CustomObjectsApi);

  return {
    kc,
    k8sApi,
    customObjectsApi
  }
}

const main = async() => {
  console.log('controller starting  up');
  const { customObjectsApi, kc } = await getApis();

  const watch = new k8s.Watch(kc);

  const startWatch = async () => {
    await watch.watch(
      `/apis/${GROUP}/${VERSION}/${PLURAL}`,
      { allowWatchBookmarks: true },
      async (type, obj: PgResource) => {
        if (type === 'ADDED' || type === 'MODIFIED') {
          console.log('Adding or modifying type');
          await customObjectsApi.patchNamespacedCustomObjectStatus({
            group: GROUP,
            plural: PLURAL,
            version: VERSION,
            name: obj.metadata.name,
            namespace: obj.metadata.namespace,
            body: {
              phase: 'Ready',
              message: 'Resource created'
            }
          });
        } else if (type === 'DELETED') {
          console.log('Deleting type');
           await customObjectsApi.patchNamespacedCustomObjectStatus({
            group: GROUP,
            plural: PLURAL,
            version: VERSION,
            name: obj.metadata.name,
            namespace: obj.metadata.namespace,
            body: {
              phase: 'Deleted',
              message: 'Resource created'
            }
          });
        }


        console.log(JSON.stringify(obj));

      },
      (error: any) => {
        console.error('Watch error: ', error.message);
        console.log('Restarting watch in 5 seconds...');
        setTimeout(startWatch, 5000);
      }
    )
  }

  // await watch.watch(
  //   `/apis/${GROUP}/${VERSION}/${PLURAL}`,
  //   { allowWatchBookmarks: true },
  //   async (type, obj) => {
  //     if (type === 'ADDED' || type === 'MODIFIED') {
  //       console.log('Adding or reconciling resource');
  //     } else {
  //       console.log('')
  //     }
  //   }
  // )
};

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

main().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
